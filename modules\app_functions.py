# ///////////////////////////////////////////////////////////////
#
# BY: WANDERSON M.PIMENTA
# PROJECT MADE WITH: Qt Designer and PySide6
# V: 1.0.0
#
# This project can be used freely for all uses, as long as they maintain the
# respective credits only in the Python scripts, any information in the visual
# interface (GUI) can be modified without any implication.
#
# There are limitations on Qt licenses if you want to use your products
# commercially, I recommend reading them on the official website:
# https://doc.qt.io/qtforpython/licenses.html
#
# ///////////////////////////////////////////////////////////////

# MAIN FILE
# ///////////////////////////////////////////////////////////////
from PySide6.QtCore import *
from PySide6.QtGui import *
from PySide6.QtWidgets import *
from .app_settings import Settings

# WITH ACCESS TO MAIN WINDOW WIDGETS
# ///////////////////////////////////////////////////////////////
class AppFunctions(QMainWindow):
    def setThemeHack(self):
        Settings.BTN_LEFT_BOX_COLOR = "background-color: #495474;"
        Settings.BTN_RIGHT_BOX_COLOR = "background-color: #495474;"
        Settings.MENU_SELECTED_STYLESHEET = """
        border-left: 22px solid qlineargradient(spread:pad, x1:0.034, y1:0, x2:0.216, y2:0, stop:0.499 rgba(255, 121, 198, 255), stop:0.5 rgba(85, 170, 255, 0));
        background-color: #566388;
        """

        # SET MANUAL STYLES
        try:
            self.ui.lineEdit.setStyleSheet("background-color: #6272a4;")
        except:
            pass
        try:
            self.ui.pushButton.setStyleSheet("background-color: #6272a4;")
        except:
            pass
        try:
            self.ui.plainTextEdit.setStyleSheet("background-color: #6272a4;")
        except:
            pass
        try:
            self.ui.tableWidget.setStyleSheet("QScrollBar:vertical { background: #6272a4; } QScrollBar:horizontal { background: #6272a4; }")
        except:
            pass
        try:
            self.ui.scrollArea.setStyleSheet("QScrollBar:vertical { background: #6272a4; } QScrollBar:horizontal { background: #6272a4; }")
        except:
            pass
        try:
            self.ui.comboBox.setStyleSheet("background-color: #6272a4;")
        except:
            pass
        try:
            self.ui.horizontalScrollBar.setStyleSheet("background-color: #6272a4;")
        except:
            pass
        try:
            self.ui.verticalScrollBar.setStyleSheet("background-color: #6272a4;")
        except:
            pass
        try:
            self.ui.commandLinkButton.setStyleSheet("color: #ff79c6;")
        except:
            pass
